# 使用官方 MySQL 8.0 镜像作为基础镜像
FROM mysql:8.0

# 设置维护者信息
LABEL maintainer="wangzhixin"

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=root
ENV MYSQL_DATABASE=dormitory_management
ENV MYSQL_USER=app_user
ENV MYSQL_PASSWORD=app_password

# 复制自定义配置文件
COPY my.cnf /etc/mysql/conf.d/

# 复制初始化脚本
COPY init.sql /docker-entrypoint-initdb.d/

# 暴露端口
EXPOSE 3306

# 设置数据目录权限
RUN chown -R mysql:mysql /var/lib/mysql

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD mysqladmin ping -h localhost -u root -p$MYSQL_ROOT_PASSWORD || exit 1
