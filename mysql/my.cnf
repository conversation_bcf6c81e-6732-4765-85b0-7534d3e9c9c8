[mysqld]
# 基本设置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# 连接设置
max_connections=200
max_connect_errors=10

# 缓存设置
innodb_buffer_pool_size=256M
query_cache_size=32M
query_cache_type=1

# 日志设置
log-error=/var/log/mysql/error.log
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 安全设置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# 时区设置
default-time-zone='+08:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
